'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Award,
  Download,
  Eye,
  Share2,
  Calendar,
  CheckCircle
} from 'lucide-react';

export default function StudentCertificatesPage() {
  // Mock data - in real app, this would come from API
  const certificates = [
    {
      id: 1,
      courseName: 'Chemistry Basics',
      courseCode: 'CHEM101',
      certificateId: 'CERT-2024-001',
      completedAt: '2024-07-30',
      finalScore: 95,
      instructor: 'Prof<PERSON> <PERSON>',
      status: 'issued',
      downloadUrl: '#'
    }
  ];

  const pendingCertificates = [
    {
      id: 2,
      courseName: 'Introduction to Algebra',
      courseCode: 'MATH101',
      progress: 85,
      estimatedCompletion: '2024-08-15',
      status: 'in_progress'
    }
  ];

  const handleDownload = (certificateId: string) => {
    // TODO: Implement certificate download
    console.log('Downloading certificate:', certificateId);
  };

  const handleShare = (certificateId: string) => {
    // TODO: Implement certificate sharing
    console.log('Sharing certificate:', certificateId);
  };

  const handlePreview = (certificateId: string) => {
    // TODO: Implement certificate preview
    console.log('Previewing certificate:', certificateId);
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Certificates</h1>
          <p className='text-muted-foreground'>
            View and download your course completion certificates
          </p>
        </div>
      </div>

      {/* Earned Certificates */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Award className='h-5 w-5' />
            <span>Earned Certificates</span>
          </CardTitle>
          <CardDescription>
            Certificates you have earned by completing courses
          </CardDescription>
        </CardHeader>
        <CardContent>
          {certificates.length > 0 ? (
            <div className='space-y-4'>
              {certificates.map((cert) => (
                <div key={cert.id} className='rounded-lg border p-6'>
                  <div className='flex items-start justify-between'>
                    <div className='flex-1 space-y-3'>
                      <div className='flex items-center space-x-3'>
                        <div className='flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-yellow-400 to-yellow-600'>
                          <Award className='h-6 w-6 text-white' />
                        </div>
                        <div>
                          <h3 className='text-lg font-semibold'>
                            {cert.courseName}
                          </h3>
                          <p className='text-muted-foreground text-sm'>
                            Course Code: {cert.courseCode}
                          </p>
                        </div>
                      </div>

                      <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>
                        <div>
                          <p className='text-muted-foreground'>
                            Certificate ID
                          </p>
                          <p className='font-mono'>{cert.certificateId}</p>
                        </div>
                        <div>
                          <p className='text-muted-foreground'>Completed</p>
                          <p>
                            {new Date(cert.completedAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <p className='text-muted-foreground'>Final Score</p>
                          <p className='font-semibold'>{cert.finalScore}%</p>
                        </div>
                        <div>
                          <p className='text-muted-foreground'>Instructor</p>
                          <p>{cert.instructor}</p>
                        </div>
                      </div>

                      <Badge variant='default' className='w-fit'>
                        <CheckCircle className='mr-1 h-3 w-3' />
                        Verified Certificate
                      </Badge>
                    </div>

                    <div className='ml-4 flex flex-col space-y-2'>
                      <Button
                        size='sm'
                        onClick={() => handleDownload(cert.certificateId)}
                      >
                        <Download className='mr-2 h-4 w-4' />
                        Download
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handlePreview(cert.certificateId)}
                      >
                        <Eye className='mr-2 h-4 w-4' />
                        Preview
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleShare(cert.certificateId)}
                      >
                        <Share2 className='mr-2 h-4 w-4' />
                        Share
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className='py-8 text-center'>
              <Award className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>
                No certificates yet
              </h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                Complete a course to earn your first certificate.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pending Certificates */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Calendar className='h-5 w-5' />
            <span>Certificates in Progress</span>
          </CardTitle>
          <CardDescription>
            Courses you&apos;re working on that will earn certificates
          </CardDescription>
        </CardHeader>
        <CardContent>
          {pendingCertificates.length > 0 ? (
            <div className='space-y-4'>
              {pendingCertificates.map((cert) => (
                <div key={cert.id} className='rounded-lg border p-6'>
                  <div className='flex items-center justify-between'>
                    <div className='flex-1 space-y-3'>
                      <div className='flex items-center space-x-3'>
                        <div className='flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-gray-400 to-gray-600'>
                          <Award className='h-6 w-6 text-white' />
                        </div>
                        <div>
                          <h3 className='text-lg font-semibold'>
                            {cert.courseName}
                          </h3>
                          <p className='text-muted-foreground text-sm'>
                            Course Code: {cert.courseCode}
                          </p>
                        </div>
                      </div>

                      <div className='space-y-2'>
                        <div className='flex items-center justify-between text-sm'>
                          <span>Progress</span>
                          <span>{cert.progress}%</span>
                        </div>
                        <div className='bg-muted h-2 w-full rounded-full'>
                          <div
                            className='bg-primary h-2 rounded-full'
                            style={{ width: `${cert.progress}%` }}
                          />
                        </div>
                        <p className='text-muted-foreground text-sm'>
                          Estimated completion:{' '}
                          {new Date(
                            cert.estimatedCompletion
                          ).toLocaleDateString()}
                        </p>
                      </div>

                      <Badge variant='outline' className='w-fit'>
                        In Progress
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className='py-8 text-center'>
              <Calendar className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>
                No courses in progress
              </h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                Enroll in a course to start working towards a certificate.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Certificate Information */}
      <Card>
        <CardHeader>
          <CardTitle>About Certificates</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid gap-4 md:grid-cols-2'>
            <div>
              <h4 className='mb-2 font-semibold'>Self-paced Courses</h4>
              <ul className='text-muted-foreground space-y-1 text-sm'>
                <li>
                  • Certificates are automatically generated upon completion
                </li>
                <li>• Must achieve minimum score on all quizzes</li>
                <li>• Complete all modules and chapters</li>
                <li>• Available for immediate download</li>
              </ul>
            </div>
            <div>
              <h4 className='mb-2 font-semibold'>Verified Courses</h4>
              <ul className='text-muted-foreground space-y-1 text-sm'>
                <li>• Requires manual verification by instructor</li>
                <li>• May include additional assessments</li>
                <li>• Higher credibility and recognition</li>
                <li>• Processing time: 1-3 business days</li>
              </ul>
            </div>
          </div>

          <div className='border-t pt-4'>
            <h4 className='mb-2 font-semibold'>Certificate Features</h4>
            <ul className='text-muted-foreground space-y-1 text-sm'>
              <li>• Unique certificate ID for verification</li>
              <li>• Digital signature and timestamp</li>
              <li>• Shareable on professional networks</li>
              <li>• PDF format for easy printing</li>
              <li>• Permanent record in your profile</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
