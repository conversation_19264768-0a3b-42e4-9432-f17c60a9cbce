'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Users,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  BookOpen
} from 'lucide-react';
import Link from 'next/link';

export default function ClassesPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - in real app, this would come from API
  const classes = [
    {
      id: 1,
      name: 'Mathematics Grade 10A',
      description: 'Advanced mathematics for grade 10 students',
      studentCount: 32,
      courseCount: 3,
      createdAt: '2024-01-15',
      status: 'active'
    },
    {
      id: 2,
      name: 'Physics Grade 11B',
      description: 'Basic physics concepts and experiments',
      studentCount: 28,
      courseCount: 2,
      createdAt: '2024-02-20',
      status: 'active'
    },
    {
      id: 3,
      name: 'Chemistry Grade 12A',
      description: 'Advanced chemistry for final year students',
      studentCount: 25,
      courseCount: 4,
      createdAt: '2024-03-10',
      status: 'active'
    }
  ];

  const filteredClasses = classes.filter(
    (classItem) =>
      classItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      classItem.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Classes</h1>
          <p className='text-muted-foreground'>
            Manage your classes and student groups
          </p>
        </div>
        <Link href='/dashboard/teacher/classes/new'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Create Class
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Classes</CardTitle>
          <CardDescription>View and manage all your classes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search classes...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Class Name</TableHead>
                  <TableHead>Students</TableHead>
                  <TableHead>Courses</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredClasses.map((classItem) => (
                  <TableRow key={classItem.id}>
                    <TableCell>
                      <div className='space-y-1'>
                        <p className='font-medium'>{classItem.name}</p>
                        <p className='text-muted-foreground text-sm'>
                          {classItem.description}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-1'>
                        <Users className='text-muted-foreground h-4 w-4' />
                        <span>{classItem.studentCount}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-1'>
                        <BookOpen className='text-muted-foreground h-4 w-4' />
                        <span>{classItem.courseCount}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          classItem.status === 'active'
                            ? 'default'
                            : 'secondary'
                        }
                      >
                        {classItem.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className='text-sm'>
                        {new Date(classItem.createdAt).toLocaleDateString()}
                      </span>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' className='h-8 w-8 p-0'>
                            <MoreHorizontal className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/classes/${classItem.id}`}
                            >
                              <Edit className='mr-2 h-4 w-4' />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/classes/${classItem.id}/students`}
                            >
                              <Users className='mr-2 h-4 w-4' />
                              Manage Students
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem className='text-red-600'>
                            <Trash2 className='mr-2 h-4 w-4' />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredClasses.length === 0 && (
            <div className='py-8 text-center'>
              <Users className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>No classes found</h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                {searchTerm
                  ? 'Try adjusting your search terms.'
                  : 'Get started by creating a new class.'}
              </p>
              {!searchTerm && (
                <div className='mt-6'>
                  <Link href='/dashboard/teacher/classes/new'>
                    <Button>
                      <Plus className='mr-2 h-4 w-4' />
                      Create Class
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
