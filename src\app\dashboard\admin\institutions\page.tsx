'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Building2,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Users
} from 'lucide-react';
import Link from 'next/link';

export default function InstitutionsPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - in real app, this would come from API
  const institutions = [
    {
      id: 1,
      name: 'SMA Negeri 1 Jakarta',
      type: 'sma-negeri',
      plan: 'pro',
      billingCycle: 'yearly',
      studentCount: 450,
      teacherCount: 25,
      paymentStatus: 'paid',
      paymentDueDate: '2024-12-15',
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      name: 'Universitas Indonesia',
      type: 'university-negeri',
      plan: 'enterprise',
      billingCycle: 'yearly',
      studentCount: 2500,
      teacherCount: 150,
      paymentStatus: 'paid',
      paymentDueDate: '2024-11-30',
      createdAt: '2023-08-20'
    },
    {
      id: 3,
      name: 'SD Swasta Bina Bangsa',
      type: 'sd-swasta',
      plan: 'basic',
      billingCycle: 'monthly',
      studentCount: 120,
      teacherCount: 8,
      paymentStatus: 'unpaid',
      paymentDueDate: '2024-08-15',
      createdAt: '2024-06-10'
    },
    {
      id: 4,
      name: 'SMP Negeri 5 Bandung',
      type: 'smp-negeri',
      plan: 'pro',
      billingCycle: 'monthly',
      studentCount: 320,
      teacherCount: 18,
      paymentStatus: 'paid',
      paymentDueDate: '2024-09-01',
      createdAt: '2024-03-22'
    }
  ];

  const filteredInstitutions = institutions.filter(
    (institution) =>
      institution.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      institution.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    return (
      <Badge variant={status === 'paid' ? 'default' : 'destructive'}>
        {status}
      </Badge>
    );
  };

  const getPlanBadge = (plan: string) => {
    const variant =
      plan === 'enterprise'
        ? 'default'
        : plan === 'pro'
          ? 'secondary'
          : 'outline';
    return <Badge variant={variant}>{plan}</Badge>;
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Institutions</h1>
          <p className='text-muted-foreground'>
            Manage all educational institutions on the platform
          </p>
        </div>
        <Link href='/dashboard/admin/institutions/new'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Add Institution
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Institutions</CardTitle>
          <CardDescription>
            View and manage all registered institutions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search institutions...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Institution</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Plan</TableHead>
                  <TableHead>Students/Teachers</TableHead>
                  <TableHead>Payment Status</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInstitutions.map((institution) => (
                  <TableRow key={institution.id}>
                    <TableCell>
                      <div className='flex items-center space-x-2'>
                        <Building2 className='text-muted-foreground h-4 w-4' />
                        <div>
                          <p className='font-medium'>{institution.name}</p>
                          <p className='text-muted-foreground text-sm'>
                            ID: {institution.id}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant='outline'>{institution.type}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className='space-y-1'>
                        {getPlanBadge(institution.plan)}
                        <p className='text-muted-foreground text-xs'>
                          {institution.billingCycle}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-1'>
                        <Users className='text-muted-foreground h-3 w-3' />
                        <span className='text-sm'>
                          {institution.studentCount}/{institution.teacherCount}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(institution.paymentStatus)}
                    </TableCell>
                    <TableCell>
                      <span className='text-sm'>
                        {new Date(
                          institution.paymentDueDate
                        ).toLocaleDateString()}
                      </span>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' className='h-8 w-8 p-0'>
                            <MoreHorizontal className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/admin/institutions/${institution.id}`}
                            >
                              <Edit className='mr-2 h-4 w-4' />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem className='text-red-600'>
                            <Trash2 className='mr-2 h-4 w-4' />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredInstitutions.length === 0 && (
            <div className='py-8 text-center'>
              <Building2 className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>
                No institutions found
              </h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                {searchTerm
                  ? 'Try adjusting your search terms.'
                  : 'Get started by adding a new institution.'}
              </p>
              {!searchTerm && (
                <div className='mt-6'>
                  <Link href='/dashboard/admin/institutions/new'>
                    <Button>
                      <Plus className='mr-2 h-4 w-4' />
                      Add Institution
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
