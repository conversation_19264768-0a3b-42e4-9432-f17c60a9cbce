'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  UserCheck,
  Search,
  MoreHorizontal,
  Eye,
  UserX,
  Copy,
  QrCode
} from 'lucide-react';

export default function EnrollmentsPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - in real app, this would come from API
  const enrollmentRequests = [
    {
      id: 1,
      studentName: '<PERSON>',
      studentEmail: '<EMAIL>',
      courseName: 'Introduction to Algebra',
      courseCode: 'MATH101',
      requestDate: '2024-08-01',
      status: 'pending'
    },
    {
      id: 2,
      studentName: 'Jane Smith',
      studentEmail: '<EMAIL>',
      courseName: 'Physics Fundamentals',
      courseCode: 'PHYS101',
      requestDate: '2024-08-02',
      status: 'pending'
    }
  ];

  const activeEnrollments = [
    {
      id: 1,
      studentName: 'Alice Johnson',
      studentEmail: '<EMAIL>',
      courseName: 'Introduction to Algebra',
      courseCode: 'MATH101',
      enrolledDate: '2024-07-15',
      progress: 75,
      status: 'active'
    },
    {
      id: 2,
      studentName: 'Bob Wilson',
      studentEmail: '<EMAIL>',
      courseName: 'Physics Fundamentals',
      courseCode: 'PHYS101',
      enrolledDate: '2024-07-20',
      progress: 45,
      status: 'active'
    },
    {
      id: 3,
      studentName: 'Carol Brown',
      studentEmail: '<EMAIL>',
      courseName: 'Chemistry Basics',
      courseCode: 'CHEM101',
      enrolledDate: '2024-07-25',
      progress: 100,
      status: 'completed'
    }
  ];

  const courseCodes = [
    { code: 'MATH101', name: 'Introduction to Algebra', students: 45 },
    { code: 'PHYS101', name: 'Physics Fundamentals', students: 32 },
    { code: 'CHEM101', name: 'Chemistry Basics', students: 28 }
  ];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const handleApproveEnrollment = (id: number) => {
    // TODO: Implement API call to approve enrollment
    console.log('Approving enrollment:', id);
  };

  const handleRejectEnrollment = (id: number) => {
    // TODO: Implement API call to reject enrollment
    console.log('Rejecting enrollment:', id);
  };

  const filteredRequests = enrollmentRequests.filter(
    (request) =>
      request.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.studentEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.courseName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredEnrollments = activeEnrollments.filter(
    (enrollment) =>
      enrollment.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      enrollment.studentEmail
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      enrollment.courseName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Student Enrollments
          </h1>
          <p className='text-muted-foreground'>
            Manage student enrollments and course access
          </p>
        </div>
      </div>

      <Tabs defaultValue='requests' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='requests'>Enrollment Requests</TabsTrigger>
          <TabsTrigger value='active'>Active Enrollments</TabsTrigger>
          <TabsTrigger value='codes'>Course Codes</TabsTrigger>
        </TabsList>

        <TabsContent value='requests'>
          <Card>
            <CardHeader>
              <CardTitle>Pending Enrollment Requests</CardTitle>
              <CardDescription>
                Review and approve student enrollment requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='mb-4 flex items-center space-x-2'>
                <div className='relative flex-1'>
                  <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
                  <Input
                    placeholder='Search requests...'
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className='pl-8'
                  />
                </div>
              </div>

              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Request Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[100px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>{request.studentName}</p>
                            <p className='text-muted-foreground text-sm'>
                              {request.studentEmail}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>{request.courseName}</p>
                            <code className='bg-muted rounded px-1 text-sm'>
                              {request.courseCode}
                            </code>
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(request.requestDate).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Badge variant='outline'>{request.status}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className='flex space-x-2'>
                            <Button
                              size='sm'
                              onClick={() =>
                                handleApproveEnrollment(request.id)
                              }
                            >
                              <UserCheck className='h-3 w-3' />
                            </Button>
                            <Button
                              size='sm'
                              variant='outline'
                              onClick={() => handleRejectEnrollment(request.id)}
                            >
                              <UserX className='h-3 w-3' />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {filteredRequests.length === 0 && (
                <div className='py-8 text-center'>
                  <UserCheck className='text-muted-foreground mx-auto h-12 w-12' />
                  <h3 className='mt-2 text-sm font-semibold'>
                    No pending requests
                  </h3>
                  <p className='text-muted-foreground mt-1 text-sm'>
                    All enrollment requests have been processed.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='active'>
          <Card>
            <CardHeader>
              <CardTitle>Active Enrollments</CardTitle>
              <CardDescription>
                View all active student enrollments and their progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='mb-4 flex items-center space-x-2'>
                <div className='relative flex-1'>
                  <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
                  <Input
                    placeholder='Search enrollments...'
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className='pl-8'
                  />
                </div>
              </div>

              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Enrolled Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[70px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEnrollments.map((enrollment) => (
                      <TableRow key={enrollment.id}>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>
                              {enrollment.studentName}
                            </p>
                            <p className='text-muted-foreground text-sm'>
                              {enrollment.studentEmail}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>
                              {enrollment.courseName}
                            </p>
                            <code className='bg-muted rounded px-1 text-sm'>
                              {enrollment.courseCode}
                            </code>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <div className='flex items-center space-x-2'>
                              <div className='bg-muted h-2 w-16 rounded-full'>
                                <div
                                  className='bg-primary h-2 rounded-full'
                                  style={{ width: `${enrollment.progress}%` }}
                                />
                              </div>
                              <span className='text-sm'>
                                {enrollment.progress}%
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(
                            enrollment.enrolledDate
                          ).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              enrollment.status === 'completed'
                                ? 'default'
                                : 'secondary'
                            }
                          >
                            {enrollment.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' className='h-8 w-8 p-0'>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuItem>
                                <Eye className='mr-2 h-4 w-4' />
                                View Progress
                              </DropdownMenuItem>
                              <DropdownMenuItem className='text-red-600'>
                                <UserX className='mr-2 h-4 w-4' />
                                Remove Student
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='codes'>
          <Card>
            <CardHeader>
              <CardTitle>Course Codes</CardTitle>
              <CardDescription>
                Share these codes with students for easy enrollment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='grid gap-4'>
                {courseCodes.map((course) => (
                  <div
                    key={course.code}
                    className='flex items-center justify-between rounded-lg border p-4'
                  >
                    <div className='space-y-1'>
                      <p className='font-medium'>{course.name}</p>
                      <p className='text-muted-foreground text-sm'>
                        {course.students} students enrolled
                      </p>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <code className='bg-muted rounded px-3 py-2 font-mono text-lg'>
                        {course.code}
                      </code>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => copyToClipboard(course.code)}
                      >
                        <Copy className='h-4 w-4' />
                      </Button>
                      <Button variant='outline' size='sm'>
                        <QrCode className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
