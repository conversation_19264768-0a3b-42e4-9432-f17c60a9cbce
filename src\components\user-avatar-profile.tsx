import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface UserAvatarProfileProps {
  className?: string;
  showInfo?: boolean;
  user?: {
    imageUrl?: string;
    fullName?: string | null;
    emailAddresses?: Array<{ emailAddress: string }>;
  } | null;
}

export function UserAvatarProfile({
  className,
  showInfo = false,
  user
}: UserAvatarProfileProps) {
  const defaultUser = {
    imageUrl: '',
    fullName: 'Guest',
    emailAddresses: [{ emailAddress: '' }]
  };
  const displayUser = user || defaultUser;

  return (
    <div className='flex items-center gap-2'>
      <Avatar className={className}>
        <AvatarImage
          src={displayUser.imageUrl}
          alt={displayUser.fullName || ''}
        />
        <AvatarFallback className='rounded-lg'>
          {displayUser.fullName?.slice(0, 2)?.toUpperCase() || 'CN'}
        </AvatarFallback>
      </Avatar>

      {showInfo && (
        <div className='grid flex-1 text-left text-sm leading-tight'>
          <span className='truncate font-semibold'>
            {displayUser.fullName || ''}
          </span>
          <span className='truncate text-xs'>
            {displayUser.emailAddresses?.[0]?.emailAddress || ''}
          </span>
        </div>
      )}
    </div>
  );
}
