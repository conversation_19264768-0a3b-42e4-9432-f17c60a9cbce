import { NextRequest, NextResponse } from 'next/server';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import { db, users } from '@/lib/db';
import { LoginCredentials, ApiResponse, AuthUser } from '@/types/database';

export async function POST(request: NextRequest) {
  try {
    const body: LoginCredentials = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email and password are required'
        } as ApiResponse,
        { status: 400 }
      );
    }

    // Find user by email
    const user = await db.query.users.findFirst({
      where: eq(users.email, email.toLowerCase()),
      with: {
        institution: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid email or password' } as ApiResponse,
        { status: 401 }
      );
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return NextResponse.json(
        { success: false, error: 'Invalid email or password' } as ApiResponse,
        { status: 401 }
      );
    }

    // Create auth user object (without password)
    const authUser: AuthUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      institutionId: user.institutionId || undefined
    };

    return NextResponse.json({
      success: true,
      data: {
        user: authUser,
        institution: user.institution
      },
      message: 'Sign in successful'
    } as ApiResponse);
  } catch (error) {
    console.error('Sign in error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    );
  }
}
