'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Users, Bot, TrendingUp, Plus } from 'lucide-react';
import Link from 'next/link';

export default function TeacherDashboard() {
  // Mock data - in real app, this would come from API
  const stats = {
    totalClasses: 5,
    totalCourses: 12,
    totalStudents: 150,
    completionRate: 78
  };

  const recentCourses = [
    {
      id: 1,
      name: 'Introduction to Mathematics',
      type: 'self_paced',
      students: 45,
      completion: 85,
      status: 'active'
    },
    {
      id: 2,
      name: 'Basic Physics',
      type: 'verified',
      students: 32,
      completion: 72,
      status: 'active'
    },
    {
      id: 3,
      name: 'Chemistry Fundamentals',
      type: 'self_paced',
      students: 28,
      completion: 90,
      status: 'completed'
    }
  ];

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Teacher Dashboard
          </h1>
          <p className='text-muted-foreground'>
            Manage your classes, courses, and track student progress
          </p>
        </div>
        <div className='flex space-x-2'>
          <Link href='/dashboard/teacher/courses/generate'>
            <Button variant='outline'>
              <Bot className='mr-2 h-4 w-4' />
              AI Generator
            </Button>
          </Link>
          <Link href='/dashboard/teacher/courses/new'>
            <Button>
              <Plus className='mr-2 h-4 w-4' />
              Create Course
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Classes</CardTitle>
            <Users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalClasses}</div>
            <p className='text-muted-foreground text-xs'>Active classes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Courses</CardTitle>
            <BookOpen className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalCourses}</div>
            <p className='text-muted-foreground text-xs'>Published courses</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Students
            </CardTitle>
            <Users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalStudents}</div>
            <p className='text-muted-foreground text-xs'>Enrolled students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Completion Rate
            </CardTitle>
            <TrendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.completionRate}%</div>
            <p className='text-muted-foreground text-xs'>Average completion</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Courses */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Courses</CardTitle>
          <CardDescription>Your latest course activities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {recentCourses.map((course) => (
              <div
                key={course.id}
                className='flex items-center justify-between rounded-lg border p-4'
              >
                <div className='space-y-1'>
                  <p className='font-medium'>{course.name}</p>
                  <div className='flex items-center space-x-2'>
                    <Badge
                      variant={
                        course.type === 'verified' ? 'default' : 'secondary'
                      }
                    >
                      {course.type}
                    </Badge>
                    <Badge
                      variant={
                        course.status === 'active' ? 'default' : 'outline'
                      }
                    >
                      {course.status}
                    </Badge>
                    <span className='text-muted-foreground text-sm'>
                      {course.students} students
                    </span>
                  </div>
                </div>
                <div className='flex items-center space-x-4'>
                  <div className='text-right'>
                    <p className='text-sm font-medium'>{course.completion}%</p>
                    <p className='text-muted-foreground text-xs'>completion</p>
                  </div>
                  <Link href={`/dashboard/teacher/courses/${course.id}`}>
                    <Button variant='outline' size='sm'>
                      View
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
          <div className='mt-4'>
            <Link href='/dashboard/teacher/courses'>
              <Button variant='outline' className='w-full'>
                View All Courses
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
