import { GoogleGenAI } from '@google/genai';
import { CourseOutline, GeneratedContent } from '@/types/database';

// Initialize Gemini AI
const getGeminiAI = () => {
  const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error('GEMINI_API_KEY is not configured');
  }
  return new GoogleGenAI({ apiKey });
};

// Process PDF using Gemini's document understanding capabilities
export const processPDFWithGemini = async (file: File): Promise<string> => {
  try {
    const ai = getGeminiAI();

    // Convert file to base64
    const arrayBuffer = await file.arrayBuffer();
    const base64Content = Buffer.from(arrayBuffer).toString('base64');

    const contents = [
      {
        text: `Analyze this educational PDF document and extract all the content in a structured format.
        Focus on:
        1. Main educational content and concepts
        2. Learning objectives and goals
        3. Chapter/section structure
        4. Key topics and subtopics
        5. Important definitions and explanations
        6. Examples and case studies

        Please provide a comprehensive extraction that can be used to create an educational course.
        Ignore headers, footers, page numbers, and formatting artifacts.`
      },
      {
        inlineData: {
          mimeType: 'application/pdf',
          data: base64Content
        }
      }
    ];

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: contents
    });

    return response.text || '';
  } catch (error) {
    console.error('Error processing PDF with Gemini:', error);
    throw new Error('Failed to process PDF file');
  }
};

// Generate course outline from extracted text with quizzes
export const generateCourseOutline = async (
  extractedText: string,
  courseInfo: {
    courseName?: string;
    courseDescription?: string;
    targetAudience?: string;
    difficulty?: string;
  }
): Promise<CourseOutline> => {
  try {
    const ai = getGeminiAI();

    const contents = [
      {
        text: `Based on the following extracted text from a PDF document, create a comprehensive course outline with integrated quizzes.

        Course Information:
        - Name: ${courseInfo.courseName || 'Generated Course'}
        - Description: ${courseInfo.courseDescription || 'AI-generated course'}
        - Target Audience: ${courseInfo.targetAudience || 'General learners'}
        - Difficulty Level: ${courseInfo.difficulty || 'Beginner'}

        Extracted Content:
        ${extractedText}

        IMPORTANT: Create a course structure that includes quizzes at different levels:
        1. Chapter Quiz - after each chapter
        2. Module Quiz - after completing all chapters in a module
        3. Final Exam - after completing all modules

        Please create a structured course outline with the following JSON format:
        {
          "courseName": "Course Name",
          "description": "Course description",
          "modules": [
            {
              "name": "Module Name",
              "description": "Module description",
              "chapters": [
                {
                  "name": "Chapter Name",
                  "description": "Chapter description",
                  "hasQuiz": true
                }
              ],
              "hasModuleQuiz": true
            }
          ],
          "hasFinalExam": true
        }

        Make sure:
        - 3-6 modules total
        - 3-5 chapters per module
        - Each chapter has a quiz
        - Each module has a comprehensive quiz
        - Course has a final exam
        - Content follows logical learning progression
        - Appropriate for the target audience and difficulty level

        Return ONLY the JSON, no additional text.`
      }
    ];

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: contents
    });

    const text = response.text || '';

    // Parse the JSON response
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const outline = JSON.parse(jsonMatch[0]);
        return outline as CourseOutline;
      } else {
        throw new Error('No valid JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing JSON response:', parseError);
      // Return a fallback outline
      return createFallbackOutline(courseInfo);
    }
  } catch (error) {
    console.error('Error generating course outline:', error);
    throw new Error('Failed to generate course outline');
  }
};

// Generate content for a specific chapter with quiz
export const generateChapterContent = async (
  chapterName: string,
  chapterDescription: string,
  moduleContext: string,
  courseContext: string,
  sourceText?: string
): Promise<GeneratedContent> => {
  try {
    const ai = getGeminiAI();

    const contents = [
      {
        text: `Generate comprehensive educational content for a chapter in a course.

        Context:
        - Course: ${courseContext}
        - Module: ${moduleContext}
        - Chapter: ${chapterName}
        - Description: ${chapterDescription}
        ${sourceText ? `- Source Material: ${sourceText.substring(0, 2000)}...` : ''}

        Please generate:
        1. Detailed chapter content in Markdown format (1000-2000 words)
        2. A chapter quiz with 5-10 questions (mix of multiple choice, true/false, and essay questions)

        The content should be:
        - Educational and engaging
        - Well-structured with headings and subheadings
        - Include examples and practical applications
        - Appropriate for the learning level

        Return the response in the following JSON format:
        {
          "content": "# Chapter Title\\n\\nMarkdown content here...",
          "quiz": {
            "name": "Chapter Quiz: ${chapterName}",
            "description": "Quiz for ${chapterName}",
            "timeLimit": 20,
            "minimumScore": 70,
            "questions": [
              {
                "type": "multiple_choice",
                "question": "Question text",
                "options": ["Option A", "Option B", "Option C", "Option D"],
                "correctAnswer": "Option A",
                "points": 1
              },
              {
                "type": "true_false",
                "question": "Question text",
                "correctAnswer": "true",
                "points": 1
              },
              {
                "type": "essay",
                "question": "Question text",
                "correctAnswer": "Sample answer or key points",
                "points": 3
              }
            ]
          }
        }

        Return ONLY the JSON, no additional text.`
      }
    ];

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: contents
    });

    const text = response.text || '';

    // Parse the JSON response
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const content = JSON.parse(jsonMatch[0]);
        return content as GeneratedContent;
      } else {
        throw new Error('No valid JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing JSON response:', parseError);
      // Return fallback content
      return createFallbackContent(chapterName, chapterDescription);
    }
  } catch (error) {
    console.error('Error generating chapter content:', error);
    throw new Error('Failed to generate chapter content');
  }
};

// Fallback outline when AI generation fails
const createFallbackOutline = (courseInfo: any): CourseOutline => {
  return {
    courseName: courseInfo.courseName || 'Generated Course',
    description:
      courseInfo.courseDescription || 'AI-generated course description',
    modules: [
      {
        name: 'Introduction and Fundamentals',
        description: 'Basic concepts and introduction to the subject',
        chapters: [
          {
            name: 'Chapter 1: Overview',
            description: 'Introduction to the topic',
            hasQuiz: true
          },
          {
            name: 'Chapter 2: Basic Concepts',
            description: 'Fundamental principles',
            hasQuiz: true
          },
          {
            name: 'Chapter 3: Key Terms',
            description: 'Important terminology',
            hasQuiz: true
          }
        ],
        hasModuleQuiz: true
      },
      {
        name: 'Core Concepts',
        description: 'Main topics and detailed explanations',
        chapters: [
          {
            name: 'Chapter 4: Advanced Topics',
            description: 'In-depth coverage',
            hasQuiz: true
          },
          {
            name: 'Chapter 5: Practical Applications',
            description: 'Real-world examples',
            hasQuiz: true
          }
        ],
        hasModuleQuiz: true
      },
      {
        name: 'Advanced Topics',
        description: 'Complex concepts and case studies',
        chapters: [
          {
            name: 'Chapter 6: Case Studies',
            description: 'Practical examples',
            hasQuiz: true
          },
          {
            name: 'Chapter 7: Future Trends',
            description: 'Emerging developments',
            hasQuiz: true
          }
        ],
        hasModuleQuiz: true
      }
    ],
    hasFinalExam: true
  };
};

// Generate module quiz
export const generateModuleQuiz = async (
  moduleName: string,
  moduleDescription: string,
  chapters: string[],
  courseContext: string
): Promise<any> => {
  try {
    const ai = getGeminiAI();

    const contents = [
      {
        text: `Generate a comprehensive module quiz for an educational course.

        Context:
        - Course: ${courseContext}
        - Module: ${moduleName}
        - Description: ${moduleDescription}
        - Chapters covered: ${chapters.join(', ')}

        Create a module quiz that:
        1. Tests understanding across all chapters in the module
        2. Has 10-15 questions of mixed types
        3. Includes more challenging questions than chapter quizzes
        4. Has a higher time limit and minimum score

        Return the response in the following JSON format:
        {
          "name": "Module Quiz: ${moduleName}",
          "description": "Comprehensive quiz covering all topics in ${moduleName}",
          "timeLimit": 45,
          "minimumScore": 75,
          "questions": [
            {
              "type": "multiple_choice",
              "question": "Question text",
              "options": ["Option A", "Option B", "Option C", "Option D"],
              "correctAnswer": "Option A",
              "points": 2
            },
            {
              "type": "true_false",
              "question": "Question text",
              "correctAnswer": "true",
              "points": 1
            },
            {
              "type": "essay",
              "question": "Question text",
              "correctAnswer": "Sample answer or key points",
              "points": 5
            }
          ]
        }

        Return ONLY the JSON, no additional text.`
      }
    ];

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: contents
    });

    const text = response.text || '';
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    throw new Error('Failed to generate module quiz');
  } catch (error) {
    console.error('Error generating module quiz:', error);
    throw new Error('Failed to generate module quiz');
  }
};

// Generate final exam
export const generateFinalExam = async (
  courseName: string,
  courseDescription: string,
  modules: string[],
  courseContext: string
): Promise<any> => {
  try {
    const ai = getGeminiAI();

    const contents = [
      {
        text: `Generate a comprehensive final exam for an educational course.

        Context:
        - Course: ${courseName}
        - Description: ${courseDescription}
        - Modules covered: ${modules.join(', ')}
        - Full course context: ${courseContext}

        Create a final exam that:
        1. Tests understanding across all modules and chapters
        2. Has 20-30 questions of mixed types
        3. Includes the most challenging questions
        4. Has a longer time limit and higher minimum score
        5. Tests both knowledge and application

        Return the response in the following JSON format:
        {
          "name": "Final Exam: ${courseName}",
          "description": "Comprehensive final examination covering all course materials",
          "timeLimit": 90,
          "minimumScore": 80,
          "questions": [
            {
              "type": "multiple_choice",
              "question": "Question text",
              "options": ["Option A", "Option B", "Option C", "Option D"],
              "correctAnswer": "Option A",
              "points": 3
            },
            {
              "type": "true_false",
              "question": "Question text",
              "correctAnswer": "true",
              "points": 2
            },
            {
              "type": "essay",
              "question": "Question text",
              "correctAnswer": "Sample answer or key points",
              "points": 10
            }
          ]
        }

        Return ONLY the JSON, no additional text.`
      }
    ];

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: contents
    });

    const text = response.text || '';
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    throw new Error('Failed to generate final exam');
  } catch (error) {
    console.error('Error generating final exam:', error);
    throw new Error('Failed to generate final exam');
  }
};

// Fallback content when AI generation fails
const createFallbackContent = (
  chapterName: string,
  chapterDescription: string
): GeneratedContent => {
  return {
    content: `# ${chapterName}\n\n${chapterDescription}\n\nThis content will be generated based on your course materials.`,
    quiz: {
      name: `Chapter Quiz: ${chapterName}`,
      description: `Quiz for ${chapterName}`,
      timeLimit: 20,
      minimumScore: 70,
      questions: [
        {
          type: 'multiple_choice',
          question: 'Sample multiple choice question',
          options: ['Option A', 'Option B', 'Option C', 'Option D'],
          correctAnswer: 'Option A',
          points: 1
        },
        {
          type: 'true_false',
          question: 'Sample true/false question',
          correctAnswer: 'true',
          points: 1
        }
      ]
    }
  };
};
