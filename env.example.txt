# =================================================================
# Database Configuration (Neon DB)
# =================================================================
# Neon Database URL
DATABASE_URL=

# =================================================================
# AI Configuration (Gemini)
# =================================================================
# Google Gemini API Key
GEMINI_API_KEY=

# =================================================================
# Authentication Configuration
# =================================================================
# JWT Secret for session management
JWT_SECRET=your-super-secret-jwt-key-here

# Authentication Redirect URLs
NEXT_PUBLIC_SIGN_IN_URL="/auth/sign-in"
NEXT_PUBLIC_SIGN_UP_URL="/auth/sign-up"
NEXT_PUBLIC_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_AFTER_SIGN_UP_URL="/dashboard"


# =================================================================
# Error Tracking Configuration (Sentry)
# =================================================================
# To set up Sentry error tracking:
# 1. Create an account at https://sentry.io
# 2. Create a new project for Next.js
# 3. Follow the setup instructions below

# Step 1: Sentry DSN (Required)
# Found at: Settings > Projects > [Your Project] > Client Keys (DSN)

NEXT_PUBLIC_SENTRY_DSN=    #Example: https://****@****.ingest.sentry.io/****


# Step 2: Organization & Project Details
# Found at: Settings > Organization > General Settings

NEXT_PUBLIC_SENTRY_ORG=  # Example: acme-corp
NEXT_PUBLIC_SENTRY_PROJECT=  # Example: nextjs-dashboard


# Step 3: Sentry Auth Token

# Sentry can automatically provide readable stack traces for errors using source maps, requiring a Sentry auth token.
# More info: https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#step-4-add-readable-stack-traces-with-source-maps-optional

SENTRY_AUTH_TOKEN=    #Example: sntrys_************************************


# Step 4: Environment Control (Optional)
# Set to 'true' to disable Sentry in development

NEXT_PUBLIC_SENTRY_DISABLED= "false"


# =================================================================
# Important Notes:
# =================================================================
# 1. Rename this file to '.env' for local development
# 2. Never commit the actual '.env' file to version control
# 3. Make sure to replace all placeholder values with real ones
# 4. Keep your secret keys private and never share them
