'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Building2, Users, CreditCard, TrendingUp, Plus } from 'lucide-react';
import Link from 'next/link';

export default function AdminDashboard() {
  // Mock data - in real app, this would come from API
  const stats = {
    totalInstitutions: 24,
    totalUsers: 1250,
    totalRevenue: 125000000,
    activeSubscriptions: 22
  };

  const recentInstitutions = [
    {
      id: 1,
      name: 'SMA Negeri 1 Jakarta',
      type: 'sma-negeri',
      plan: 'pro',
      students: 450,
      status: 'paid'
    },
    {
      id: 2,
      name: 'Universitas Indonesia',
      type: 'university-negeri',
      plan: 'enterprise',
      students: 2500,
      status: 'paid'
    },
    {
      id: 3,
      name: 'SD Swasta Bina Bangsa',
      type: 'sd-swasta',
      plan: 'basic',
      students: 120,
      status: 'unpaid'
    }
  ];

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Super Admin Dashboard
          </h1>
          <p className='text-muted-foreground'>
            Manage institutions, subscriptions, and system-wide analytics
          </p>
        </div>
        <Link href='/dashboard/admin/institutions/new'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Add Institution
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Institutions
            </CardTitle>
            <Building2 className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalInstitutions}</div>
            <p className='text-muted-foreground text-xs'>+2 from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Users</CardTitle>
            <Users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {stats.totalUsers.toLocaleString()}
            </div>
            <p className='text-muted-foreground text-xs'>
              +180 from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Revenue</CardTitle>
            <CreditCard className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              Rp {(stats.totalRevenue / 1000000).toFixed(1)}M
            </div>
            <p className='text-muted-foreground text-xs'>
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Active Subscriptions
            </CardTitle>
            <TrendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {stats.activeSubscriptions}
            </div>
            <p className='text-muted-foreground text-xs'>+1 from last month</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Institutions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Institutions</CardTitle>
          <CardDescription>
            Latest institutions added to the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {recentInstitutions.map((institution) => (
              <div
                key={institution.id}
                className='flex items-center justify-between rounded-lg border p-4'
              >
                <div className='space-y-1'>
                  <p className='font-medium'>{institution.name}</p>
                  <div className='flex items-center space-x-2'>
                    <Badge variant='outline'>{institution.type}</Badge>
                    <Badge
                      variant={
                        institution.plan === 'enterprise'
                          ? 'default'
                          : 'secondary'
                      }
                    >
                      {institution.plan}
                    </Badge>
                    <span className='text-muted-foreground text-sm'>
                      {institution.students} students
                    </span>
                  </div>
                </div>
                <div className='flex items-center space-x-2'>
                  <Badge
                    variant={
                      institution.status === 'paid' ? 'default' : 'destructive'
                    }
                  >
                    {institution.status}
                  </Badge>
                  <Link
                    href={`/dashboard/admin/institutions/${institution.id}`}
                  >
                    <Button variant='outline' size='sm'>
                      View
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
          <div className='mt-4'>
            <Link href='/dashboard/admin/institutions'>
              <Button variant='outline' className='w-full'>
                View All Institutions
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
