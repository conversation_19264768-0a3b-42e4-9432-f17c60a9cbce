import { eq } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import {
  courses,
  modules,
  chapters,
  quizzes,
  questions
} from '@/lib/db/schema';
import {
  generateCourseOutline,
  generateCha<PERSON>erContent,
  generateModuleQuiz,
  generateFinalExam
} from '@/lib/gemini';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { courseOutline, teacherId, institutionId } = body;

    // Create the course
    const [course] = await db
      .insert(courses)
      .values({
        name: courseOutline.courseName,
        description: courseOutline.description,
        type: 'self_paced',
        teacherId: teacherId,
        institutionId: institutionId,
        courseCode: generateCourseCode(),
        startDate: new Date(),
        endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) // 90 days from now
      })
      .returning();

    // Create modules and chapters with quizzes
    for (
      let moduleIndex = 0;
      moduleIndex < courseOutline.modules.length;
      moduleIndex++
    ) {
      const moduleData = courseOutline.modules[moduleIndex];

      // Create module
      const [module] = await db
        .insert(modules)
        .values({
          name: moduleData.name,
          description: moduleData.description,
          courseId: course.id,
          orderIndex: moduleIndex + 1
        })
        .returning();

      // Create chapters with quizzes
      for (
        let chapterIndex = 0;
        chapterIndex < moduleData.chapters.length;
        chapterIndex++
      ) {
        const chapterData = moduleData.chapters[chapterIndex];

        // Create chapter
        const [chapter] = await db
          .insert(chapters)
          .values({
            name: chapterData.name,
            moduleId: module.id,
            orderIndex: chapterIndex + 1,
            content: `# ${chapterData.name}\n\n${chapterData.description}\n\nContent will be generated...`
          })
          .returning();

        // Create chapter quiz if specified
        if (chapterData.hasQuiz) {
          try {
            // Generate quiz content with AI
            const chapterContent = await generateChapterContent(
              chapterData.name,
              chapterData.description,
              moduleData.name,
              courseOutline.courseName
            );

            if (chapterContent.quiz) {
              // Create quiz
              const [quiz] = await db
                .insert(quizzes)
                .values({
                  name: chapterContent.quiz.name,
                  description: chapterContent.quiz.description,
                  chapterId: chapter.id,
                  courseId: course.id,
                  quizType: 'chapter',
                  timeLimit: chapterContent.quiz.timeLimit,
                  minimumScore: chapterContent.quiz.minimumScore.toString()
                })
                .returning();

              // Create questions
              for (let i = 0; i < chapterContent.quiz.questions.length; i++) {
                const questionData = chapterContent.quiz.questions[i];
                await db.insert(questions).values({
                  quizId: quiz.id,
                  type: questionData.type,
                  question: questionData.question,
                  options: questionData.options
                    ? JSON.stringify(questionData.options)
                    : null,
                  correctAnswer: questionData.correctAnswer,
                  points: questionData.points.toString(),
                  orderIndex: i + 1
                });
              }
            }

            // Update chapter with generated content
            await db
              .update(chapters)
              .set({ content: chapterContent.content })
              .where(eq(chapters.id, chapter.id));
          } catch (error) {
            console.error('Error generating chapter content:', error);
            // Continue with basic quiz if AI generation fails
            const [quiz] = await db
              .insert(quizzes)
              .values({
                name: `Chapter Quiz: ${chapterData.name}`,
                description: `Quiz for ${chapterData.name}`,
                chapterId: chapter.id,
                courseId: course.id,
                quizType: 'chapter',
                timeLimit: 20,
                minimumScore: '70'
              })
              .returning();

            // Add a basic question
            await db.insert(questions).values({
              quizId: quiz.id,
              type: 'multiple_choice',
              question: 'What is the main topic of this chapter?',
              options: JSON.stringify([
                'Option A',
                'Option B',
                'Option C',
                'Option D'
              ]),
              correctAnswer: 'Option A',
              points: '1',
              orderIndex: 1
            });
          }
        }
      }

      // Create module quiz if specified
      if (moduleData.hasModuleQuiz) {
        try {
          const moduleQuizData = await generateModuleQuiz(
            moduleData.name,
            moduleData.description,
            moduleData.chapters.map((c: { name: string }) => c.name),
            courseOutline.courseName
          );

          const [moduleQuiz] = await db
            .insert(quizzes)
            .values({
              name: moduleQuizData.name,
              description: moduleQuizData.description,
              moduleId: module.id,
              courseId: course.id,
              quizType: 'module',
              timeLimit: moduleQuizData.timeLimit,
              minimumScore: moduleQuizData.minimumScore.toString()
            })
            .returning();

          // Create questions for module quiz
          for (let i = 0; i < moduleQuizData.questions.length; i++) {
            const questionData = moduleQuizData.questions[i];
            await db.insert(questions).values({
              quizId: moduleQuiz.id,
              type: questionData.type,
              question: questionData.question,
              options: questionData.options
                ? JSON.stringify(questionData.options)
                : null,
              correctAnswer: questionData.correctAnswer,
              points: questionData.points.toString(),
              orderIndex: i + 1
            });
          }
        } catch (error) {
          console.error('Error generating module quiz:', error);
          // Create basic module quiz
          const [moduleQuiz] = await db
            .insert(quizzes)
            .values({
              name: `Module Quiz: ${moduleData.name}`,
              description: `Comprehensive quiz for ${moduleData.name}`,
              moduleId: module.id,
              courseId: course.id,
              quizType: 'module',
              timeLimit: 45,
              minimumScore: '75'
            })
            .returning();
        }
      }
    }

    // Create final exam if specified
    if (courseOutline.hasFinalExam) {
      try {
        const finalExamData = await generateFinalExam(
          courseOutline.courseName,
          courseOutline.description,
          courseOutline.modules.map((m: { name: string }) => m.name),
          courseOutline.courseName
        );

        const [finalExam] = await db
          .insert(quizzes)
          .values({
            name: finalExamData.name,
            description: finalExamData.description,
            courseId: course.id,
            quizType: 'final',
            timeLimit: finalExamData.timeLimit,
            minimumScore: finalExamData.minimumScore.toString()
          })
          .returning();

        // Create questions for final exam
        for (let i = 0; i < finalExamData.questions.length; i++) {
          const questionData = finalExamData.questions[i];
          await db.insert(questions).values({
            quizId: finalExam.id,
            type: questionData.type,
            question: questionData.question,
            options: questionData.options
              ? JSON.stringify(questionData.options)
              : null,
            correctAnswer: questionData.correctAnswer,
            points: questionData.points.toString(),
            orderIndex: i + 1
          });
        }
      } catch (error) {
        console.error('Error generating final exam:', error);
        // Create basic final exam
        const [finalExam] = await db
          .insert(quizzes)
          .values({
            name: `Final Exam: ${courseOutline.courseName}`,
            description: `Comprehensive final examination`,
            courseId: course.id,
            quizType: 'final',
            timeLimit: 90,
            minimumScore: '80'
          })
          .returning();
      }
    }

    return NextResponse.json({
      success: true,
      data: { course },
      message: 'Course generated successfully with integrated quizzes'
    });
  } catch (error) {
    console.error('Error generating course:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate course',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function generateCourseCode(): string {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}
