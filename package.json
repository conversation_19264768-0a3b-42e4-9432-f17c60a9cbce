{"name": "next-shadcn-dashboard-starter", "version": "1.0.0", "private": true, "author": {"name": "<PERSON>", "url": "https://github.com/Kiranism"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint src --fix && pnpm format", "lint:strict": "eslint --max-warnings=0 src", "format": "prettier --write .", "format:check": "prettier -c -w .", "prepare": "husky", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,css,less,scss,sass}": ["prettier --write --no-error-on-unmatched-pattern"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@google/generative-ai": "^0.24.1", "@heroui/react": "^2.8.2", "@hookform/resolvers": "^3.9.1", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@sentry/nextjs": "^9.19.0", "@tabler/icons-react": "^3.31.0", "@tailwindcss/postcss": "^4.0.0", "@tanstack/react-table": "^8.21.2", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "eslint": "8.48.0", "eslint-config-next": "15.1.0", "hugeicons-react": "^0.3.0", "input-otp": "^1.4.2", "kbar": "^0.1.0-beta.45", "lucide-react": "^0.476.0", "match-sorter": "^8.0.0", "motion": "^11.17.0", "next": "15.3.2", "next-themes": "^0.4.6", "nextjs-toploader": "^3.7.15", "nuqs": "^2.4.1", "postcss": "8.4.49", "react": "19.0.0", "react-datepicker": "^8.4.0", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-responsive": "^10.0.0", "recharts": "^2.15.1", "sharp": "^0.33.5", "sonner": "^1.7.1", "sort-by": "^1.2.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2", "uuid": "^11.0.3", "vaul": "^1.1.2", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@faker-js/faker": "^9.3.0", "@types/node": "22.10.2", "@types/react": "19.0.1", "@types/react-dom": "19.0.2", "@types/sort-by": "^1.2.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "husky": "^9.1.7", "lint-staged": "^15.2.11", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tw-animate-css": "^1.2.4"}, "overrides": {"@types/react": "19.0.1", "@types/react-dom": "19.0.2"}}