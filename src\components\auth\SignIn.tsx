'use client';

import { buttonVariants } from '@/components/ui/button';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { authStorage, getRedirectPath } from '@/lib/auth';
import { ApiResponse, AuthUser } from '@/types/database';
import { Metadata } from 'next';
import Link from 'next/link';
import { useState } from 'react';
import Image from 'next/image';
import { Quicksand } from 'next/font/google';

const quicksand = Quicksand({
  subsets: ['latin'],
  variable: '--font-quicksand',
  weight: ['300', '400', '500', '600', '700']
});

export const metadata: Metadata = {
  title: 'Authentication',
  description: 'Authentication forms built using the components.'
};

export default function SignInViewPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data: ApiResponse<{ user: AuthUser; institution?: any }> =
        await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Sign in failed');
      }

      if (!data.data?.user) {
        throw new Error('Invalid response from server');
      }

      // Store user data in localStorage
      authStorage.setUser(data.data.user);

      // Redirect based on user role
      const redirectUrl = getRedirectPath(data.data.user);
      window.location.href = redirectUrl;
    } catch (error) {
      console.error('Sign in error:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className={`${quicksand.variable} font-quicksand relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0`}
    >
      <Link
        href='/examples/authentication'
        className={cn(
          buttonVariants({ variant: 'ghost' }),
          'absolute top-4 right-4 hidden md:top-8 md:right-8'
        )}
      >
        Login
      </Link>
      <div className='bg-muted relative hidden h-full flex-col p-10 text-white lg:flex dark:border-r'>
        <div className='absolute inset-0 bg-zinc-900' />
        <div className='relative z-20 flex text-lg font-medium'>
          <Link
            href='/'
            className='group mr-4 rounded-xl transition-transform duration-300 hover:scale-105 focus:ring-4 focus:ring-sky-300 focus:outline-none md:mr-8 lg:p-0'
            aria-label='Terang LMS - Your Learning Management Partner'
          >
            <Image
              src='https://cdn.terang.ai/images/logo/logo-terang-ai.svg'
              alt='Terang Logo'
              width={300}
              height={300}
              className='h-32 w-32 md:h-32 md:w-32 lg:h-36 lg:w-36'
            />
          </Link>
          {/* KinderLib */}
        </div>
        <div className='relative z-20 mt-auto'>
          <blockquote className='space-y-2'>
            <p className='text-lg'>Your Learning Management Partner</p>
            <footer className='text-sm'>Terang AI</footer>
          </blockquote>
        </div>
      </div>
      <div className='flex h-full items-center justify-center p-4 lg:p-8'>
        <div className='flex w-full max-w-md flex-col items-center justify-center space-y-6'>
          {/* github link  */}
          <Link
            className={cn('group inline-flex hover:text-yellow-200')}
            target='_blank'
            href={'https://github.com/kiranism/next-shadcn-dashboard-starter'}
          ></Link>

          {/* Custom Sign In Form */}
          <div className='w-full space-y-4'>
            <div className='flex flex-col space-y-2 text-center'>
              <h1 className='text-2xl font-semibold tracking-tight'>
                Sign in to your account
              </h1>
              <p className='text-muted-foreground text-sm'>
                Enter your email and password below
              </p>
            </div>

            <form onSubmit={handleSubmit} className='space-y-4'>
              {error && (
                <div className='rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-600'>
                  {error}
                </div>
              )}

              <div className='space-y-2'>
                <Label htmlFor='email'>Email</Label>
                <Input
                  id='email'
                  type='email'
                  placeholder='<EMAIL>'
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={isLoading}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='password'>Password</Label>
                <Input
                  id='password'
                  type='password'
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isLoading}
                />
              </div>

              <Button type='submit' className='w-full' disabled={isLoading}>
                {isLoading ? 'Signing in...' : 'Sign In'}
              </Button>
            </form>

            <div className='text-center text-sm'>
              <Link
                href='/forgot-password'
                className='hover:text-primary underline underline-offset-4'
              >
                Forgot your password?
              </Link>
            </div>

            <div className='text-center text-sm'>
              Don&apos;t have an account?{' '}
              <Link
                href='/auth/sign-up'
                className='hover:text-primary underline underline-offset-4'
              >
                Sign up
              </Link>
            </div>
          </div>

          <p className='text-muted-foreground px-8 text-center text-sm'>
            By clicking continue, you agree to our{' '}
            <Link
              href='/terms'
              className='hover:text-primary underline underline-offset-4'
            >
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link
              href='/privacy'
              className='hover:text-primary underline underline-offset-4'
            >
              Privacy Policy
            </Link>
            .
          </p>
        </div>
      </div>
    </div>
  );
}
