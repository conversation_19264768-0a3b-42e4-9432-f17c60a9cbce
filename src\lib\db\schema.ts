import { relations } from 'drizzle-orm';
import {
  boolean,
  integer,
  json,
  pgEnum,
  pgTable,
  serial,
  text,
  timestamp,
  varchar,
  decimal
} from 'drizzle-orm/pg-core';

// Enums
export const userRoleEnum = pgEnum('user_role', [
  'student',
  'teacher',
  'super_admin'
]);
export const institutionTypeEnum = pgEnum('institution_type', [
  'sd-negeri',
  'sd-swasta',
  'smp-negeri',
  'smp-swasta',
  'sma-negeri',
  'sma-swasta',
  'university-negeri',
  'university-swasta',
  'institution-training',
  'institution-course',
  'institution-other'
]);
export const subscriptionPlanEnum = pgEnum('subscription_plan', [
  'basic',
  'pro',
  'enterprise'
]);
export const billingCycleEnum = pgEnum('billing_cycle', ['monthly', 'yearly']);
export const paymentStatusEnum = pgEnum('payment_status', ['paid', 'unpaid']);
export const courseTypeEnum = pgEnum('course_type', ['self_paced', 'verified']);
export const questionTypeEnum = pgEnum('question_type', [
  'multiple_choice',
  'true_false',
  'essay'
]);

// Users table
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password: varchar('password', { length: 255 }).notNull(),
  role: userRoleEnum('role').notNull().default('student'),
  institutionId: integer('institution_id').references(() => institutions.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Institutions table
export const institutions = pgTable('institutions', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  type: institutionTypeEnum('type').notNull(),
  subscriptionPlan: subscriptionPlanEnum('subscription_plan')
    .notNull()
    .default('basic'),
  billingCycle: billingCycleEnum('billing_cycle').notNull().default('monthly'),
  paymentStatus: paymentStatusEnum('payment_status')
    .notNull()
    .default('unpaid'),
  paymentDueDate: timestamp('payment_due_date'),
  studentCount: integer('student_count').default(0),
  teacherCount: integer('teacher_count').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Classes table
export const classes = pgTable('classes', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  institutionId: integer('institution_id')
    .references(() => institutions.id)
    .notNull(),
  teacherId: integer('teacher_id')
    .references(() => users.id)
    .notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Courses table
export const courses = pgTable('courses', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  type: courseTypeEnum('type').notNull().default('self_paced'),
  startDate: timestamp('start_date'),
  endDate: timestamp('end_date'),
  teacherId: integer('teacher_id')
    .references(() => users.id)
    .notNull(),
  institutionId: integer('institution_id')
    .references(() => institutions.id)
    .notNull(),
  courseCode: varchar('course_code', { length: 50 }).unique(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Course enrollments (many-to-many between courses and classes)
export const courseEnrollments = pgTable('course_enrollments', {
  id: serial('id').primaryKey(),
  courseId: integer('course_id')
    .references(() => courses.id)
    .notNull(),
  classId: integer('class_id')
    .references(() => classes.id)
    .notNull(),
  enrolledAt: timestamp('enrolled_at').defaultNow().notNull()
});

// Student enrollments (many-to-many between students and courses)
export const studentEnrollments = pgTable('student_enrollments', {
  id: serial('id').primaryKey(),
  studentId: integer('student_id')
    .references(() => users.id)
    .notNull(),
  courseId: integer('course_id')
    .references(() => courses.id)
    .notNull(),
  enrolledAt: timestamp('enrolled_at').defaultNow().notNull(),
  completedAt: timestamp('completed_at'),
  finalScore: decimal('final_score', { precision: 5, scale: 2 }),
  certificateGenerated: boolean('certificate_generated').default(false)
});

// Modules table
export const modules = pgTable('modules', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  courseId: integer('course_id')
    .references(() => courses.id)
    .notNull(),
  orderIndex: integer('order_index').notNull(),
  startDate: timestamp('start_date'),
  endDate: timestamp('end_date'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Chapters table
export const chapters = pgTable('chapters', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  content: text('content'), // Markdown content
  moduleId: integer('module_id')
    .references(() => modules.id)
    .notNull(),
  orderIndex: integer('order_index').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Quizzes table
export const quizzes = pgTable('quizzes', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  chapterId: integer('chapter_id').references(() => chapters.id),
  moduleId: integer('module_id').references(() => modules.id),
  courseId: integer('course_id').references(() => courses.id),
  quizType: varchar('quiz_type', { length: 50 }).notNull().default('chapter'), // 'chapter', 'module', 'final'
  minimumScore: decimal('minimum_score', { precision: 5, scale: 2 })
    .notNull()
    .default('70'),
  timeLimit: integer('time_limit'), // in minutes
  startDate: timestamp('start_date'),
  endDate: timestamp('end_date'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Questions table
export const questions = pgTable('questions', {
  id: serial('id').primaryKey(),
  quizId: integer('quiz_id')
    .references(() => quizzes.id)
    .notNull(),
  type: questionTypeEnum('type').notNull(),
  question: text('question').notNull(),
  options: json('options'), // For multiple choice questions
  correctAnswer: text('correct_answer').notNull(),
  points: decimal('points', { precision: 5, scale: 2 }).notNull().default('1'),
  orderIndex: integer('order_index').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Quiz attempts table
export const quizAttempts = pgTable('quiz_attempts', {
  id: serial('id').primaryKey(),
  studentId: integer('student_id')
    .references(() => users.id)
    .notNull(),
  quizId: integer('quiz_id')
    .references(() => quizzes.id)
    .notNull(),
  score: decimal('score', { precision: 5, scale: 2 }),
  totalPoints: decimal('total_points', { precision: 5, scale: 2 }),
  passed: boolean('passed').default(false),
  startedAt: timestamp('started_at').defaultNow().notNull(),
  completedAt: timestamp('completed_at'),
  answers: json('answers'), // Store student answers
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Student progress table
export const studentProgress = pgTable('student_progress', {
  id: serial('id').primaryKey(),
  studentId: integer('student_id')
    .references(() => users.id)
    .notNull(),
  courseId: integer('course_id')
    .references(() => courses.id)
    .notNull(),
  moduleId: integer('module_id').references(() => modules.id),
  chapterId: integer('chapter_id').references(() => chapters.id),
  completed: boolean('completed').default(false),
  completedAt: timestamp('completed_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Relations
export const usersRelations = relations(users, ({ one, many }) => ({
  institution: one(institutions, {
    fields: [users.institutionId],
    references: [institutions.id]
  }),
  teachingClasses: many(classes),
  teachingCourses: many(courses),
  studentEnrollments: many(studentEnrollments),
  quizAttempts: many(quizAttempts),
  progress: many(studentProgress)
}));

export const institutionsRelations = relations(institutions, ({ many }) => ({
  users: many(users),
  classes: many(classes),
  courses: many(courses)
}));

export const classesRelations = relations(classes, ({ one, many }) => ({
  institution: one(institutions, {
    fields: [classes.institutionId],
    references: [institutions.id]
  }),
  teacher: one(users, {
    fields: [classes.teacherId],
    references: [users.id]
  }),
  courseEnrollments: many(courseEnrollments)
}));

export const coursesRelations = relations(courses, ({ one, many }) => ({
  teacher: one(users, {
    fields: [courses.teacherId],
    references: [users.id]
  }),
  institution: one(institutions, {
    fields: [courses.institutionId],
    references: [institutions.id]
  }),
  modules: many(modules),
  courseEnrollments: many(courseEnrollments),
  studentEnrollments: many(studentEnrollments),
  quizzes: many(quizzes),
  progress: many(studentProgress)
}));

export const modulesRelations = relations(modules, ({ one, many }) => ({
  course: one(courses, {
    fields: [modules.courseId],
    references: [courses.id]
  }),
  chapters: many(chapters),
  quizzes: many(quizzes),
  progress: many(studentProgress)
}));

export const chaptersRelations = relations(chapters, ({ one, many }) => ({
  module: one(modules, {
    fields: [chapters.moduleId],
    references: [modules.id]
  }),
  quizzes: many(quizzes),
  progress: many(studentProgress)
}));

export const quizzesRelations = relations(quizzes, ({ one, many }) => ({
  chapter: one(chapters, {
    fields: [quizzes.chapterId],
    references: [chapters.id]
  }),
  module: one(modules, {
    fields: [quizzes.moduleId],
    references: [modules.id]
  }),
  course: one(courses, {
    fields: [quizzes.courseId],
    references: [courses.id]
  }),
  questions: many(questions),
  attempts: many(quizAttempts)
}));

export const questionsRelations = relations(questions, ({ one }) => ({
  quiz: one(quizzes, {
    fields: [questions.quizId],
    references: [quizzes.id]
  })
}));

export const quizAttemptsRelations = relations(quizAttempts, ({ one }) => ({
  student: one(users, {
    fields: [quizAttempts.studentId],
    references: [users.id]
  }),
  quiz: one(quizzes, {
    fields: [quizAttempts.quizId],
    references: [quizzes.id]
  })
}));

export const studentEnrollmentsRelations = relations(
  studentEnrollments,
  ({ one }) => ({
    student: one(users, {
      fields: [studentEnrollments.studentId],
      references: [users.id]
    }),
    course: one(courses, {
      fields: [studentEnrollments.courseId],
      references: [courses.id]
    })
  })
);

export const courseEnrollmentsRelations = relations(
  courseEnrollments,
  ({ one }) => ({
    course: one(courses, {
      fields: [courseEnrollments.courseId],
      references: [courses.id]
    }),
    class: one(classes, {
      fields: [courseEnrollments.classId],
      references: [classes.id]
    })
  })
);

export const studentProgressRelations = relations(
  studentProgress,
  ({ one }) => ({
    student: one(users, {
      fields: [studentProgress.studentId],
      references: [users.id]
    }),
    course: one(courses, {
      fields: [studentProgress.courseId],
      references: [courses.id]
    }),
    module: one(modules, {
      fields: [studentProgress.moduleId],
      references: [modules.id]
    }),
    chapter: one(chapters, {
      fields: [studentProgress.chapterId],
      references: [chapters.id]
    })
  })
);
