'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BookOpen, Award, TrendingUp, Clock } from 'lucide-react';
import Link from 'next/link';

export default function StudentDashboard() {
  // Mock data - in real app, this would come from API
  const stats = {
    enrolledCourses: 3,
    completedCourses: 1,
    certificates: 1,
    totalHours: 24
  };

  const enrolledCourses = [
    {
      id: 1,
      name: 'Introduction to Mathematics',
      type: 'self_paced',
      progress: 85,
      status: 'in_progress',
      dueDate: '2024-12-15'
    },
    {
      id: 2,
      name: 'Basic Physics',
      type: 'verified',
      progress: 45,
      status: 'in_progress',
      dueDate: '2024-11-30'
    },
    {
      id: 3,
      name: 'Chemistry Fundamentals',
      type: 'self_paced',
      progress: 100,
      status: 'completed',
      dueDate: '2024-10-20'
    }
  ];

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Student Dashboard
          </h1>
          <p className='text-muted-foreground'>
            Track your learning progress and access your courses
          </p>
        </div>
        <Link href='/dashboard/student/courses'>
          <Button>
            <BookOpen className='mr-2 h-4 w-4' />
            Browse Courses
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Enrolled Courses
            </CardTitle>
            <BookOpen className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.enrolledCourses}</div>
            <p className='text-muted-foreground text-xs'>Active enrollments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Completed</CardTitle>
            <TrendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.completedCourses}</div>
            <p className='text-muted-foreground text-xs'>Courses completed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Certificates</CardTitle>
            <Award className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.certificates}</div>
            <p className='text-muted-foreground text-xs'>Earned certificates</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Study Hours</CardTitle>
            <Clock className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalHours}</div>
            <p className='text-muted-foreground text-xs'>Total hours</p>
          </CardContent>
        </Card>
      </div>

      {/* Enrolled Courses */}
      <Card>
        <CardHeader>
          <CardTitle>My Courses</CardTitle>
          <CardDescription>
            Your current course enrollments and progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {enrolledCourses.map((course) => (
              <div
                key={course.id}
                className='flex items-center justify-between rounded-lg border p-4'
              >
                <div className='flex-1 space-y-2'>
                  <div className='flex items-center justify-between'>
                    <p className='font-medium'>{course.name}</p>
                    <div className='flex items-center space-x-2'>
                      <Badge
                        variant={
                          course.type === 'verified' ? 'default' : 'secondary'
                        }
                      >
                        {course.type}
                      </Badge>
                      <Badge
                        variant={
                          course.status === 'completed' ? 'default' : 'outline'
                        }
                      >
                        {course.status}
                      </Badge>
                    </div>
                  </div>
                  <div className='space-y-1'>
                    <div className='flex items-center justify-between text-sm'>
                      <span>Progress</span>
                      <span>{course.progress}%</span>
                    </div>
                    <Progress value={course.progress} className='h-2' />
                  </div>
                  <p className='text-muted-foreground text-xs'>
                    Due: {new Date(course.dueDate).toLocaleDateString()}
                  </p>
                </div>
                <div className='ml-4'>
                  <Link href={`/dashboard/student/courses/${course.id}`}>
                    <Button variant='outline' size='sm'>
                      {course.status === 'completed' ? 'Review' : 'Continue'}
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
          <div className='mt-4'>
            <Link href='/dashboard/student/courses'>
              <Button variant='outline' className='w-full'>
                View All Courses
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
