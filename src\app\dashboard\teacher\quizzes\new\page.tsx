'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Save, Plus, Trash2 } from 'lucide-react';
import Link from 'next/link';

interface Question {
  id: string;
  type: 'multiple_choice' | 'true_false' | 'essay';
  question: string;
  options?: string[];
  correctAnswer: string;
  points: number;
}

export default function NewQuizPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    courseId: '',
    chapterId: '',
    moduleId: '',
    timeLimit: 30,
    minimumScore: 70,
    startDate: '',
    endDate: ''
  });
  const [questions, setQuestions] = useState<Question[]>([]);

  // Mock data - in real app, this would come from API
  const courses = [
    { id: '1', name: 'Introduction to Algebra' },
    { id: '2', name: 'Physics Fundamentals' },
    { id: '3', name: 'Chemistry Basics' }
  ];

  const chapters = [
    { id: '1', name: 'Chapter 1: Overview', moduleId: '1' },
    { id: '2', name: 'Chapter 2: Basic Concepts', moduleId: '1' },
    { id: '3', name: 'Chapter 3: Advanced Topics', moduleId: '2' }
  ];

  const addQuestion = () => {
    const newQuestion: Question = {
      id: Date.now().toString(),
      type: 'multiple_choice',
      question: '',
      options: ['', '', '', ''],
      correctAnswer: '',
      points: 1
    };
    setQuestions([...questions, newQuestion]);
  };

  const updateQuestion = (id: string, field: string, value: any) => {
    setQuestions(
      questions.map((q) => (q.id === id ? { ...q, [field]: value } : q))
    );
  };

  const removeQuestion = (id: string) => {
    setQuestions(questions.filter((q) => q.id !== id));
  };

  const updateQuestionOption = (
    questionId: string,
    optionIndex: number,
    value: string
  ) => {
    setQuestions(
      questions.map((q) =>
        q.id === questionId
          ? {
              ...q,
              options: q.options?.map((opt, idx) =>
                idx === optionIndex ? value : opt
              )
            }
          : q
      )
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Implement API call to create quiz
      console.log('Creating quiz:', { formData, questions });

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Redirect to quizzes list
      router.push('/dashboard/teacher/quizzes');
    } catch (error) {
      console.error('Error creating quiz:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderQuestionForm = (question: Question, index: number) => (
    <Card key={question.id}>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='text-lg'>Question {index + 1}</CardTitle>
          <Button
            variant='outline'
            size='sm'
            onClick={() => removeQuestion(question.id)}
          >
            <Trash2 className='h-4 w-4' />
          </Button>
        </div>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          <div className='space-y-2'>
            <Label>Question Type</Label>
            <Select
              value={question.type}
              onValueChange={(value: any) =>
                updateQuestion(question.id, 'type', value)
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='multiple_choice'>Multiple Choice</SelectItem>
                <SelectItem value='true_false'>True/False</SelectItem>
                <SelectItem value='essay'>Essay</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className='space-y-2'>
            <Label>Points</Label>
            <Input
              type='number'
              value={question.points}
              onChange={(e) =>
                updateQuestion(
                  question.id,
                  'points',
                  parseInt(e.target.value) || 1
                )
              }
              min='1'
              max='10'
            />
          </div>
        </div>

        <div className='space-y-2'>
          <Label>Question</Label>
          <Textarea
            value={question.question}
            onChange={(e) =>
              updateQuestion(question.id, 'question', e.target.value)
            }
            placeholder='Enter your question here...'
            rows={3}
          />
        </div>

        {question.type === 'multiple_choice' && (
          <div className='space-y-2'>
            <Label>Answer Options</Label>
            <div className='space-y-2'>
              {question.options?.map((option, optionIndex) => (
                <div key={optionIndex} className='flex items-center space-x-2'>
                  <Input
                    value={option}
                    onChange={(e) =>
                      updateQuestionOption(
                        question.id,
                        optionIndex,
                        e.target.value
                      )
                    }
                    placeholder={`Option ${String.fromCharCode(65 + optionIndex)}`}
                  />
                  <input
                    type='radio'
                    name={`correct-${question.id}`}
                    checked={question.correctAnswer === option}
                    onChange={() =>
                      updateQuestion(question.id, 'correctAnswer', option)
                    }
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {question.type === 'true_false' && (
          <div className='space-y-2'>
            <Label>Correct Answer</Label>
            <Select
              value={question.correctAnswer}
              onValueChange={(value) =>
                updateQuestion(question.id, 'correctAnswer', value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder='Select correct answer' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='true'>True</SelectItem>
                <SelectItem value='false'>False</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {question.type === 'essay' && (
          <div className='space-y-2'>
            <Label>Sample Answer / Key Points</Label>
            <Textarea
              value={question.correctAnswer}
              onChange={(e) =>
                updateQuestion(question.id, 'correctAnswer', e.target.value)
              }
              placeholder='Provide a sample answer or key points for grading...'
              rows={3}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/teacher/quizzes'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Create New Quiz</h1>
          <p className='text-muted-foreground'>
            Create a new quiz or assessment for your course
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className='space-y-6'>
        {/* Quiz Details */}
        <Card>
          <CardHeader>
            <CardTitle>Quiz Details</CardTitle>
            <CardDescription>Basic information about your quiz</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Quiz Name</Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder='e.g., Module 1 Assessment'
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='course'>Course</Label>
                <Select
                  value={formData.courseId}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, courseId: value }))
                  }
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select course' />
                  </SelectTrigger>
                  <SelectContent>
                    {courses.map((course) => (
                      <SelectItem key={course.id} value={course.id}>
                        {course.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>Description</Label>
              <Textarea
                id='description'
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value
                  }))
                }
                placeholder='Brief description of the quiz content'
                rows={3}
              />
            </div>

            <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
              <div className='space-y-2'>
                <Label htmlFor='timeLimit'>Time Limit (minutes)</Label>
                <Input
                  id='timeLimit'
                  type='number'
                  value={formData.timeLimit}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      timeLimit: parseInt(e.target.value) || 30
                    }))
                  }
                  min='5'
                  max='180'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='minimumScore'>Minimum Score (%)</Label>
                <Input
                  id='minimumScore'
                  type='number'
                  value={formData.minimumScore}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      minimumScore: parseInt(e.target.value) || 70
                    }))
                  }
                  min='0'
                  max='100'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='chapter'>Chapter (Optional)</Label>
                <Select
                  value={formData.chapterId}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, chapterId: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select chapter' />
                  </SelectTrigger>
                  <SelectContent>
                    {chapters.map((chapter) => (
                      <SelectItem key={chapter.id} value={chapter.id}>
                        {chapter.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Separator />

        {/* Questions Section */}
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div>
              <h2 className='text-2xl font-bold'>Questions</h2>
              <p className='text-muted-foreground'>
                Add questions to your quiz
              </p>
            </div>
            <Button type='button' onClick={addQuestion}>
              <Plus className='mr-2 h-4 w-4' />
              Add Question
            </Button>
          </div>

          {questions.length === 0 ? (
            <Card>
              <CardContent className='pt-6'>
                <div className='py-8 text-center'>
                  <p className='text-muted-foreground'>
                    No questions added yet.
                  </p>
                  <Button type='button' onClick={addQuestion} className='mt-4'>
                    <Plus className='mr-2 h-4 w-4' />
                    Add Your First Question
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className='space-y-4'>
              {questions.map((question, index) =>
                renderQuestionForm(question, index)
              )}
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className='flex justify-end space-x-4'>
          <Link href='/dashboard/teacher/quizzes'>
            <Button variant='outline' type='button'>
              Cancel
            </Button>
          </Link>
          <Button type='submit' disabled={isLoading || questions.length === 0}>
            <Save className='mr-2 h-4 w-4' />
            {isLoading ? 'Creating...' : 'Create Quiz'}
          </Button>
        </div>
      </form>
    </div>
  );
}
