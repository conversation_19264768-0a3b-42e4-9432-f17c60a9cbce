'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  BookOpen,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  Bot,
  Copy
} from 'lucide-react';
import Link from 'next/link';

export default function CoursesPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - in real app, this would come from API
  const courses = [
    {
      id: 1,
      name: 'Introduction to Algebra',
      description: 'Basic algebraic concepts and problem solving',
      type: 'self_paced',
      courseCode: 'MATH101',
      moduleCount: 8,
      studentCount: 45,
      status: 'published',
      createdAt: '2024-01-15',
      startDate: '2024-02-01',
      endDate: '2024-05-30'
    },
    {
      id: 2,
      name: 'Physics Fundamentals',
      description: 'Introduction to basic physics principles',
      type: 'verified',
      courseCode: 'PHYS101',
      moduleCount: 12,
      studentCount: 32,
      status: 'published',
      createdAt: '2024-02-20',
      startDate: '2024-03-01',
      endDate: '2024-06-15'
    },
    {
      id: 3,
      name: 'Chemistry Basics',
      description: 'Fundamental chemistry concepts and experiments',
      type: 'self_paced',
      courseCode: 'CHEM101',
      moduleCount: 6,
      studentCount: 28,
      status: 'draft',
      createdAt: '2024-03-10',
      startDate: '2024-04-01',
      endDate: '2024-07-30'
    }
  ];

  const filteredCourses = courses.filter(
    (course) =>
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.courseCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>
          <p className='text-muted-foreground'>
            Create and manage your educational courses
          </p>
        </div>
        <div className='flex space-x-2'>
          <Link href='/dashboard/teacher/courses/generate'>
            <Button variant='outline'>
              <Bot className='mr-2 h-4 w-4' />
              AI Generator
            </Button>
          </Link>
          <Link href='/dashboard/teacher/courses/new'>
            <Button>
              <Plus className='mr-2 h-4 w-4' />
              Create Course
            </Button>
          </Link>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Courses</CardTitle>
          <CardDescription>View and manage all your courses</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search courses...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Course</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Modules</TableHead>
                  <TableHead>Students</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCourses.map((course) => (
                  <TableRow key={course.id}>
                    <TableCell>
                      <div className='space-y-1'>
                        <p className='font-medium'>{course.name}</p>
                        <p className='text-muted-foreground text-sm'>
                          {course.description}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-2'>
                        <code className='bg-muted rounded px-2 py-1 text-sm'>
                          {course.courseCode}
                        </code>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => copyToClipboard(course.courseCode)}
                        >
                          <Copy className='h-3 w-3' />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          course.type === 'verified' ? 'default' : 'secondary'
                        }
                      >
                        {course.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-1'>
                        <BookOpen className='text-muted-foreground h-4 w-4' />
                        <span>{course.moduleCount}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-1'>
                        <Users className='text-muted-foreground h-4 w-4' />
                        <span>{course.studentCount}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          course.status === 'published' ? 'default' : 'outline'
                        }
                      >
                        {course.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' className='h-8 w-8 p-0'>
                            <MoreHorizontal className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/courses/${course.id}`}
                            >
                              <Edit className='mr-2 h-4 w-4' />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/courses/${course.id}/modules`}
                            >
                              <BookOpen className='mr-2 h-4 w-4' />
                              Manage Modules
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/courses/${course.id}/students`}
                            >
                              <Users className='mr-2 h-4 w-4' />
                              View Students
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem className='text-red-600'>
                            <Trash2 className='mr-2 h-4 w-4' />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredCourses.length === 0 && (
            <div className='py-8 text-center'>
              <BookOpen className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>No courses found</h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                {searchTerm
                  ? 'Try adjusting your search terms.'
                  : 'Get started by creating a new course.'}
              </p>
              {!searchTerm && (
                <div className='mt-6 flex justify-center space-x-2'>
                  <Link href='/dashboard/teacher/courses/generate'>
                    <Button variant='outline'>
                      <Bot className='mr-2 h-4 w-4' />
                      AI Generator
                    </Button>
                  </Link>
                  <Link href='/dashboard/teacher/courses/new'>
                    <Button>
                      <Plus className='mr-2 h-4 w-4' />
                      Create Course
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
