# Terang LMS - Learning Management System

A comprehensive Learning Management System built with Next.js 15, TypeScript, Tailwind CSS, Shadcn UI, and Neon Database. Features AI-powered course generation using Google Gemini 2.5 Flash.

## 🚀 Features

### Core Features
- ⚡ **Next.js 15** with App Router
- 🎨 **Tailwind CSS** for styling
- 🧩 **Shadcn UI** components
- 📱 **Responsive design**
- 🌙 **Dark/Light mode**
- 🔒 **Role-based authentication** (Student, Teacher, Super Admin)
- 🗄️ **Neon Database** with Drizzle ORM
- 🤖 **AI Course Generation** with Google Gemini 2.5 Flash

### User Roles & Features

#### Super Admin
- 🏢 **Institution Management** - CRUD operations for educational institutions
- 👥 **User Management** - Assign users to institutions and roles
- 💳 **Subscription Management** - Manage billing cycles and payment status
- 📊 **Analytics Dashboard** - System-wide analytics and reporting
- 🎯 **Institution Types** - Support for various educational institution types

#### Teacher/Admin
- 📚 **Class Management** - Create and manage classes
- 🎓 **Course Management** - Create, edit, and manage courses
- 🤖 **AI Course Generation** - Generate courses from PDF uploads using AI
- 👨‍🎓 **Student Enrollment** - Manage course enrollments by code
- 📝 **Quiz & Assessment System** - Create quizzes with multiple question types
- 📈 **Reports & Analytics** - Track student progress and validate results
- 🏆 **Certificate Management** - Generate and manage certificates

#### Student/Participant
- 📖 **Course Access** - Access enrolled courses and materials
- 📊 **Progress Tracking** - Track learning progress across courses
- 🏅 **Certificates** - View and download earned certificates
- 📝 **Quiz Taking** - Take quizzes and assessments

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, TypeScript, Tailwind CSS, Shadcn UI
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Authentication**: Custom JWT-based authentication
- **AI Integration**: Google Gemini 2.5 Flash API
- **UI Components**: Radix UI, Lucide React
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts for analytics

## 📋 Prerequisites

- Node.js 18+ 
- npm/yarn/pnpm
- Neon Database account
- Google AI API key (for Gemini 2.5 Flash)

## 🚀 Getting Started

1. **Clone the repository:**
```bash
git clone <repository-url>
cd terang-lms-ui
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
```bash
cp env.example.txt .env.local
```

Edit `.env.local` with your configuration:
```env
# Database
DATABASE_URL=your_neon_database_url

# AI Configuration
GEMINI_API_KEY=your_google_ai_api_key

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key
```

4. **Set up the database:**
```bash
npm run db:generate
npm run db:push
```

5. **Run the development server:**
```bash
npm run dev
```

6. **Open [http://localhost:3000](http://localhost:3000)** in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js 15 app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   └── dashboard/         # Role-based dashboard pages
├── components/            # Reusable UI components
│   ├── auth/              # Authentication components
│   ├── layout/            # Layout components
│   └── ui/                # Shadcn UI components
├── config/                # Configuration files
├── lib/                   # Utility functions and database
│   ├── db/                # Database schema and connection
│   └── auth.ts            # Authentication utilities
├── types/                 # TypeScript type definitions
└── constants/             # Application constants
```

## 🗄️ Database Schema

The system uses a comprehensive database schema with the following main entities:

- **Users** - System users with role-based access
- **Institutions** - Educational institutions with subscription plans
- **Classes** - Groups of students managed by teachers
- **Courses** - Educational content with modules and chapters
- **Modules** - Course sections containing chapters
- **Chapters** - Individual lessons with content and quizzes
- **Quizzes** - Assessments with multiple question types
- **Student Progress** - Tracking learning progress
- **Enrollments** - Student-course relationships

## 🔐 Authentication & Authorization

The system implements role-based access control with three main roles:

1. **Super Admin** - Full system access
2. **Teacher** - Institution and course management
3. **Student** - Course access and learning

Authentication is handled through custom JWT tokens stored in localStorage.

## 🤖 AI Integration

The system integrates with Google Gemini 2.5 Flash for:

- **Course Generation** - Create courses from PDF uploads
- **Content Creation** - Generate modules and chapters
- **Quiz Generation** - Automatically create assessments

## 📊 Subscription Plans

Three subscription tiers are available:

- **Basic** - For small schools (50-200 students)
- **Professional** - For growing schools (100-1000 students)
- **Enterprise** - For large institutions (500-5000 students)

## 🚀 Deployment

The application is ready for deployment on platforms like Vercel, Netlify, or any Node.js hosting service.

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support and questions, please contact the development team.
