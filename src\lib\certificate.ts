// Certificate generation utilities
// In a real implementation, you might use libraries like jsPDF, canvas, or server-side PDF generation

export interface CertificateData {
  studentName: string;
  courseName: string;
  courseCode: string;
  completionDate: string;
  finalScore: number;
  instructorName: string;
  institutionName: string;
  certificateId: string;
}

export const generateCertificateId = (): string => {
  const year = new Date().getFullYear();
  const randomNum = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0');
  return `CERT-${year}-${randomNum}`;
};

export const generateCertificateHTML = (data: CertificateData): string => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Certificate of Completion</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Inter:wght@400;500;600&display=swap');
            
            body {
                margin: 0;
                padding: 40px;
                font-family: 'Inter', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .certificate {
                background: white;
                width: 800px;
                height: 600px;
                padding: 60px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                border-radius: 8px;
                position: relative;
                overflow: hidden;
            }
            
            .certificate::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 8px;
                background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
            }
            
            .header {
                text-align: center;
                margin-bottom: 40px;
            }
            
            .logo {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                border-radius: 50%;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
            
            .title {
                font-family: 'Playfair Display', serif;
                font-size: 36px;
                font-weight: 700;
                color: #1a1a1a;
                margin-bottom: 10px;
            }
            
            .subtitle {
                font-size: 18px;
                color: #666;
                font-weight: 500;
            }
            
            .content {
                text-align: center;
                margin: 40px 0;
            }
            
            .awarded-to {
                font-size: 16px;
                color: #666;
                margin-bottom: 10px;
            }
            
            .student-name {
                font-family: 'Playfair Display', serif;
                font-size: 32px;
                font-weight: 700;
                color: #1a1a1a;
                margin-bottom: 30px;
                border-bottom: 2px solid #667eea;
                display: inline-block;
                padding-bottom: 5px;
            }
            
            .completion-text {
                font-size: 16px;
                color: #666;
                line-height: 1.6;
                margin-bottom: 20px;
            }
            
            .course-name {
                font-size: 24px;
                font-weight: 600;
                color: #1a1a1a;
                margin-bottom: 10px;
            }
            
            .course-code {
                font-size: 14px;
                color: #666;
                font-family: monospace;
                background: #f5f5f5;
                padding: 4px 8px;
                border-radius: 4px;
                display: inline-block;
                margin-bottom: 20px;
            }
            
            .score {
                font-size: 18px;
                font-weight: 600;
                color: #667eea;
                margin-bottom: 30px;
            }
            
            .footer {
                display: flex;
                justify-content: space-between;
                align-items: end;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #eee;
            }
            
            .signature-section {
                text-align: center;
                flex: 1;
            }
            
            .signature-line {
                border-bottom: 1px solid #333;
                width: 150px;
                margin: 0 auto 5px;
            }
            
            .signature-label {
                font-size: 12px;
                color: #666;
            }
            
            .date-section {
                text-align: center;
                flex: 1;
            }
            
            .certificate-id {
                position: absolute;
                bottom: 20px;
                right: 20px;
                font-size: 10px;
                color: #999;
                font-family: monospace;
            }
            
            .watermark {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-45deg);
                font-size: 120px;
                color: rgba(102, 126, 234, 0.05);
                font-weight: bold;
                z-index: 0;
                pointer-events: none;
            }
            
            .content-wrapper {
                position: relative;
                z-index: 1;
            }
        </style>
    </head>
    <body>
        <div class="certificate">
            <div class="watermark">TERANG</div>
            <div class="content-wrapper">
                <div class="header">
                    <div class="logo">T</div>
                    <h1 class="title">Certificate of Completion</h1>
                    <p class="subtitle">${data.institutionName}</p>
                </div>
                
                <div class="content">
                    <p class="awarded-to">This is to certify that</p>
                    <h2 class="student-name">${data.studentName}</h2>
                    <p class="completion-text">
                        has successfully completed the course
                    </p>
                    <h3 class="course-name">${data.courseName}</h3>
                    <div class="course-code">${data.courseCode}</div>
                    <p class="score">Final Score: ${data.finalScore}%</p>
                </div>
                
                <div class="footer">
                    <div class="signature-section">
                        <div class="signature-line"></div>
                        <p class="signature-label">${data.instructorName}<br>Course Instructor</p>
                    </div>
                    <div class="date-section">
                        <div class="signature-line"></div>
                        <p class="signature-label">${data.completionDate}<br>Date of Completion</p>
                    </div>
                </div>
            </div>
            <div class="certificate-id">Certificate ID: ${data.certificateId}</div>
        </div>
    </body>
    </html>
  `;
};

export const downloadCertificateAsPDF = async (
  data: CertificateData
): Promise<void> => {
  // In a real implementation, you would use a library like jsPDF or Puppeteer
  // For now, we'll create a downloadable HTML file

  const htmlContent = generateCertificateHTML(data);
  const blob = new Blob([htmlContent], { type: 'text/html' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `certificate-${data.certificateId}.html`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const previewCertificate = (data: CertificateData): void => {
  const htmlContent = generateCertificateHTML(data);
  const newWindow = window.open('', '_blank');
  if (newWindow) {
    newWindow.document.write(htmlContent);
    newWindow.document.close();
  }
};

export const shareCertificate = async (
  data: CertificateData
): Promise<void> => {
  if (navigator.share) {
    try {
      await navigator.share({
        title: `Certificate of Completion - ${data.courseName}`,
        text: `I've completed ${data.courseName} with a score of ${data.finalScore}%!`,
        url: window.location.href
      });
    } catch (error) {
      console.error('Error sharing certificate:', error);
      // Fallback to copying to clipboard
      copyToClipboard(
        `I've completed ${data.courseName} with a score of ${data.finalScore}%! Certificate ID: ${data.certificateId}`
      );
    }
  } else {
    // Fallback for browsers that don't support Web Share API
    copyToClipboard(
      `I've completed ${data.courseName} with a score of ${data.finalScore}%! Certificate ID: ${data.certificateId}`
    );
  }
};

const copyToClipboard = (text: string): void => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      // You could show a toast notification here
      console.log('Certificate details copied to clipboard');
    })
    .catch((error) => {
      console.error('Failed to copy to clipboard:', error);
    });
};
