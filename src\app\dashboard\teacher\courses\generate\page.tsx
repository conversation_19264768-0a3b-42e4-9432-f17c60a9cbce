'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Upload, Bot, FileText, Loader2 } from 'lucide-react';
import Link from 'next/link';

export default function GenerateCoursePage() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [courseOutline, setCourseOutline] = useState<any>(null);

  const [formData, setFormData] = useState({
    courseName: '',
    courseDescription: '',
    courseType: 'self_paced',
    targetAudience: '',
    difficulty: 'beginner'
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      setSelectedFile(file);
    } else {
      alert('Please select a PDF file');
    }
  };

  const handleGenerateOutline = async () => {
    if (!selectedFile) return;

    setIsLoading(true);
    setUploadProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Call Gemini API directly with file (new document understanding API)
      const outline = await generateCourseOutlineWithGemini(
        selectedFile,
        formData
      );

      setUploadProgress(100);
      setCourseOutline(outline);
      setStep(2);
    } catch (error) {
      console.error('Error generating outline:', error);
      alert('Failed to generate course outline. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const generateCourseOutlineWithGemini = async (
    file: File,
    courseData: any
  ) => {
    try {
      // Import Gemini functions dynamically to avoid SSR issues
      const { processPDFWithGemini, generateCourseOutline } = await import(
        '@/lib/gemini'
      );

      // Process PDF with Gemini's document understanding
      const extractedText = await processPDFWithGemini(file);

      // Generate course outline with integrated quizzes
      const outline = await generateCourseOutline(extractedText, {
        courseName: courseData.courseName,
        courseDescription: courseData.courseDescription,
        targetAudience: courseData.targetAudience,
        difficulty: courseData.difficulty
      });

      return outline;
    } catch (error) {
      console.error('Error with Gemini API:', error);
      // Fallback to mock data if API fails
      return {
        courseName: courseData.courseName || 'Generated Course',
        description:
          courseData.courseDescription || 'AI-generated course description',
        modules: [
          {
            name: 'Introduction and Fundamentals',
            description: 'Basic concepts and introduction to the subject',
            chapters: [
              {
                name: 'Chapter 1: Overview',
                description: 'Introduction to the topic',
                hasQuiz: true
              },
              {
                name: 'Chapter 2: Basic Concepts',
                description: 'Fundamental principles',
                hasQuiz: true
              },
              {
                name: 'Chapter 3: Key Terms',
                description: 'Important terminology',
                hasQuiz: true
              }
            ],
            hasModuleQuiz: true
          },
          {
            name: 'Core Concepts',
            description: 'Main topics and detailed explanations',
            chapters: [
              {
                name: 'Chapter 4: Advanced Topics',
                description: 'In-depth coverage',
                hasQuiz: true
              },
              {
                name: 'Chapter 5: Practical Applications',
                description: 'Real-world examples',
                hasQuiz: true
              }
            ],
            hasModuleQuiz: true
          }
        ],
        hasFinalExam: true
      };
    }
  };

  const handleCreateCourse = async () => {
    setIsLoading(true);
    try {
      // Call API to create course with integrated quizzes
      const response = await fetch('/api/courses/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          courseOutline,
          teacherId: 1, // TODO: Get from auth context
          institutionId: 1 // TODO: Get from auth context
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to create course');
      }

      // Show success message
      alert('Course created successfully with integrated quizzes!');

      // Redirect to courses list
      router.push('/dashboard/teacher/courses');
    } catch (error) {
      console.error('Error creating course:', error);
      alert('Failed to create course. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/teacher/courses'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            AI Course Generator
          </h1>
          <p className='text-muted-foreground'>
            Generate a complete course from your PDF materials using AI
          </p>
        </div>
      </div>

      {/* Progress Indicator */}
      <Card>
        <CardContent className='pt-6'>
          <div className='mb-2 flex items-center justify-between'>
            <span className='text-sm font-medium'>Step {step} of 2</span>
            <span className='text-muted-foreground text-sm'>
              {step === 1 ? 'Upload & Configure' : 'Review & Create'}
            </span>
          </div>
          <Progress value={step === 1 ? 50 : 100} className='h-2' />
        </CardContent>
      </Card>

      {step === 1 && (
        <>
          {/* Course Information */}
          <Card>
            <CardHeader>
              <CardTitle>Course Information</CardTitle>
              <CardDescription>
                Provide basic information about your course
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='courseName'>Course Name</Label>
                  <Input
                    id='courseName'
                    value={formData.courseName}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        courseName: e.target.value
                      }))
                    }
                    placeholder='e.g., Introduction to Mathematics'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='courseType'>Course Type</Label>
                  <Select
                    value={formData.courseType}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, courseType: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='self_paced'>Self-paced</SelectItem>
                      <SelectItem value='verified'>Verified</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='courseDescription'>Course Description</Label>
                <Textarea
                  id='courseDescription'
                  value={formData.courseDescription}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      courseDescription: e.target.value
                    }))
                  }
                  placeholder='Brief description of what this course covers'
                  rows={3}
                />
              </div>

              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='targetAudience'>Target Audience</Label>
                  <Input
                    id='targetAudience'
                    value={formData.targetAudience}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        targetAudience: e.target.value
                      }))
                    }
                    placeholder='e.g., High school students'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='difficulty'>Difficulty Level</Label>
                  <Select
                    value={formData.difficulty}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, difficulty: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='beginner'>Beginner</SelectItem>
                      <SelectItem value='intermediate'>Intermediate</SelectItem>
                      <SelectItem value='advanced'>Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* PDF Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Upload Course Material</CardTitle>
              <CardDescription>
                Upload a PDF file that contains your course content. The AI will
                analyze it to generate modules and chapters.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='border-muted-foreground/25 rounded-lg border-2 border-dashed p-6'>
                  <div className='text-center'>
                    <FileText className='text-muted-foreground mx-auto h-12 w-12' />
                    <div className='mt-4'>
                      <Label htmlFor='pdf-upload' className='cursor-pointer'>
                        <span className='text-primary hover:text-primary/80 text-sm font-medium'>
                          Click to upload PDF
                        </span>
                        <Input
                          id='pdf-upload'
                          type='file'
                          accept='.pdf'
                          onChange={handleFileSelect}
                          className='hidden'
                        />
                      </Label>
                      <p className='text-muted-foreground mt-1 text-sm'>
                        PDF files up to 50MB (processed client-side)
                      </p>
                    </div>
                  </div>
                </div>

                {selectedFile && (
                  <div className='bg-muted flex items-center space-x-2 rounded-lg p-3'>
                    <FileText className='text-muted-foreground h-5 w-5' />
                    <span className='text-sm font-medium'>
                      {selectedFile.name}
                    </span>
                    <span className='text-muted-foreground text-sm'>
                      ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                )}

                {isLoading && (
                  <div className='space-y-2'>
                    <div className='flex items-center space-x-2'>
                      <Loader2 className='h-4 w-4 animate-spin' />
                      <span className='text-sm'>
                        Processing PDF and generating outline...
                      </span>
                    </div>
                    <Progress value={uploadProgress} className='h-2' />
                  </div>
                )}

                <Button
                  onClick={handleGenerateOutline}
                  disabled={!selectedFile || isLoading}
                  className='w-full'
                >
                  <Bot className='mr-2 h-4 w-4' />
                  {isLoading
                    ? 'Generating Outline...'
                    : 'Generate Course Outline'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {step === 2 && courseOutline && (
        <Card>
          <CardHeader>
            <CardTitle>Generated Course Outline</CardTitle>
            <CardDescription>
              Review the AI-generated course structure. You can edit this later.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-6'>
              <div>
                <h3 className='text-lg font-semibold'>
                  {courseOutline.courseName}
                </h3>
                <p className='text-muted-foreground'>
                  {courseOutline.description}
                </p>
              </div>

              <div className='space-y-4'>
                {courseOutline.modules.map(
                  (module: any, moduleIndex: number) => (
                    <div key={moduleIndex} className='rounded-lg border p-4'>
                      <div className='mb-2 flex items-center justify-between'>
                        <h4 className='font-medium'>
                          Module {moduleIndex + 1}: {module.name}
                        </h4>
                        {module.hasModuleQuiz && (
                          <Badge variant='secondary' className='text-xs'>
                            Module Quiz
                          </Badge>
                        )}
                      </div>
                      <p className='text-muted-foreground mb-3 text-sm'>
                        {module.description}
                      </p>
                      <div className='space-y-2'>
                        {module.chapters.map(
                          (chapter: any, chapterIndex: number) => (
                            <div
                              key={chapterIndex}
                              className='bg-muted ml-4 rounded p-2'
                            >
                              <div className='flex items-center justify-between'>
                                <div>
                                  <p className='text-sm font-medium'>
                                    {chapter.name}
                                  </p>
                                  <p className='text-muted-foreground text-xs'>
                                    {chapter.description}
                                  </p>
                                </div>
                                {chapter.hasQuiz && (
                                  <Badge variant='outline' className='text-xs'>
                                    Quiz
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )
                )}

                {courseOutline.hasFinalExam && (
                  <div className='rounded-lg border bg-gradient-to-r from-blue-50 to-indigo-50 p-4'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <h4 className='font-medium'>Final Examination</h4>
                        <p className='text-muted-foreground text-sm'>
                          Comprehensive exam covering all course materials
                        </p>
                      </div>
                      <Badge variant='default'>Final Exam</Badge>
                    </div>
                  </div>
                )}
              </div>

              <div className='flex justify-end space-x-4'>
                <Button variant='outline' onClick={() => setStep(1)}>
                  Back to Edit
                </Button>
                <Button onClick={handleCreateCourse} disabled={isLoading}>
                  <Upload className='mr-2 h-4 w-4' />
                  {isLoading ? 'Creating Course...' : 'Create Course'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
